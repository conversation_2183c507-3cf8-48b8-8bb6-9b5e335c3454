import React from 'react';
import { useLocation } from 'react-router-dom';
import HeaderNav from '../Header/Header.jsx';
import FooterComponent from '../Footer/Footer.jsx';

const AppLayout = ({ children }) => {
  const location = useLocation();
  const isAuthPage = location.pathname === '/login' || location.pathname === '/signup' || location.pathname === '/';

  return (
    <div className="w-full flex flex-col min-h-screen">
      {!isAuthPage && (
        <nav className='pt-5 pr-10 bg-[#575CEE] h-20'>
          <HeaderNav />
        </nav>
      )}

      <main className={`flex-grow ${isAuthPage ? 'flex items-center justify-center' : ''}`}>
        {children}
      </main>

      {!isAuthPage && (
        <footer className='mt-5 w-full'>
          <FooterComponent />
        </footer>
      )}
    </div>
  );
};

export default AppLayout;
