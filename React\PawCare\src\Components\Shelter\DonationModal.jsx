import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "../ui/animated-modal";
import { FaHeart, FaDollarSign, FaUser, FaEnvelope, FaCreditCard, FaCalendarAlt, FaLock } from 'react-icons/fa';

const DonationModal = () => {
  const [selectedAmount, setSelectedAmount] = useState('');
  const [customAmount, setCustomAmount] = useState('');
  const [donorInfo, setDonorInfo] = useState({
    name: '',
    email: '',
    cardNumber: '',
    expiryDate: '',
    cvv: ''
  });

  const presetAmounts = [25, 50, 100, 250, 500];

  const handleAmountSelect = (amount) => {
    setSelectedAmount(amount);
    setCustomAmount('');
  };

  const handleCustomAmountChange = (e) => {
    const value = e.target.value;
    setCustomAmount(value);
    setSelectedAmount('');
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setDonorInfo(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDonate = (e) => {
    e.preventDefault();
    const donationAmount = selectedAmount || customAmount;

    if (!donationAmount || donationAmount <= 0) {
      alert('Please select or enter a valid donation amount.');
      return;
    }

    // Handle donation submission here
    console.log('Donation submitted:', {
      amount: donationAmount,
      donor: donorInfo
    });

    alert(`Thank you for your generous donation of $${donationAmount}! Your support helps us care for animals in need.`);

    // Reset form
    setSelectedAmount('');
    setCustomAmount('');
    setDonorInfo({
      name: '',
      email: '',
      cardNumber: '',
      expiryDate: '',
      cvv: ''
    });
  };

  return (
    <Modal>
      <ModalTrigger className="bg-white text-blue-500 border-2 border-blue-500 px-6 py-3 rounded-full hover:scale-105 transition duration-300 transform hover:shadow-lg shadow-blue-100 font-medium flex items-center">
        <FaHeart className="mr-2" /> Donate Now
      </ModalTrigger>
      <ModalBody>
        <ModalContent className="max-h-[80vh] overflow-y-auto">
          <div className="text-center mb-4">
            <div className="bg-red-100 p-3 rounded-full text-red-600 mb-3 w-12 h-12 flex items-center justify-center mx-auto">
              <FaHeart size={20} />
            </div>
            <h4 className="text-xl font-bold text-gray-800 mb-2">
              Make a Donation
            </h4>
            <p className="text-gray-600 text-sm">
              Your donation helps us provide food, medical care, and shelter for animals in need.
            </p>
          </div>

          <form onSubmit={handleDonate} className="space-y-4">
            {/* Donation Amount Selection */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-2">
                <FaDollarSign className="inline mr-1" />
                Select Donation Amount
              </label>
              <div className="grid grid-cols-3 gap-1.5 mb-2">
                {presetAmounts.map((amount) => (
                  <button
                    key={amount}
                    type="button"
                    onClick={() => handleAmountSelect(amount)}
                    className={`py-1.5 px-2 text-sm rounded-md border-2 transition-colors duration-200 ${
                      selectedAmount === amount
                        ? 'border-blue-500 bg-blue-50 text-blue-600'
                        : 'border-gray-300 hover:border-blue-300'
                    }`}
                  >
                    ${amount}
                  </button>
                ))}
              </div>
              <input
                type="number"
                value={customAmount}
                onChange={handleCustomAmountChange}
                placeholder="Enter custom amount"
                min="1"
                className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Donor Information */}
            <div className="space-y-3">
              <h5 className="text-sm font-semibold text-gray-800">Donor Information</h5>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  <FaUser className="inline mr-1" />
                  Full Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={donorInfo.name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter your full name"
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  <FaEnvelope className="inline mr-1" />
                  Email Address
                </label>
                <input
                  type="email"
                  name="email"
                  value={donorInfo.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter your email address"
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  <FaCreditCard className="inline mr-1" />
                  Card Number
                </label>
                <input
                  type="text"
                  name="cardNumber"
                  value={donorInfo.cardNumber}
                  onChange={handleInputChange}
                  required
                  placeholder="1234 5678 9012 3456"
                  className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    <FaCalendarAlt className="inline mr-1" />
                    Expiry Date
                  </label>
                  <input
                    type="text"
                    name="expiryDate"
                    value={donorInfo.expiryDate}
                    onChange={handleInputChange}
                    required
                    placeholder="MM/YY"
                    className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    <FaLock className="inline mr-1" />
                    CVV
                  </label>
                  <input
                    type="text"
                    name="cvv"
                    value={donorInfo.cvv}
                    onChange={handleInputChange}
                    required
                    placeholder="123"
                    className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            <div className="bg-blue-50 p-2 rounded-md">
              <p className="text-xs text-blue-800">
                <FaLock className="inline mr-1" />
                Your payment information is secure and encrypted. We never store your card details.
              </p>
            </div>
          </form>
        </ModalContent>
        <ModalFooter className="gap-3 p-3">
          <button
            type="button"
            className="px-3 py-1.5 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            type="submit"
            onClick={handleDonate}
            className="px-3 py-1.5 text-sm bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors duration-200 flex items-center"
          >
            <FaHeart className="mr-1" />
            Donate ${selectedAmount || customAmount || '0'}
          </button>
        </ModalFooter>
      </ModalBody>
    </Modal>
  );
};

export default DonationModal;
