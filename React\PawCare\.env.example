# PawCare React App Environment Variables (Vite)
# Copy this file to .env and fill in your actual values

# Backend API Configuration
VITE_API_BASE_URL=http://localhost:3000/api/v0

# Development Settings
VITE_ENVIRONMENT=development

# Instructions:
# 1. Copy this file to .env: cp .env.example .env
# 2. Replace the placeholder values with your actual configuration
# 3. Never commit your .env file to version control
# 4. Vite requires VITE_ prefix for environment variables to be accessible in the browser
