import React from 'react'
import { Fa<PERSON>nstagram, FaTwitter, FaFacebook, FaLinkedin } from 'react-icons/fa'
import GenericProductSlider from '../Slider/GenericProductSlider'


const dogBeds = [
  {
    id: 1,
    title: 'Orthopedic Dog Bed',
    img: 'https://tse3.mm.bing.net/th?id=OIP._CO04FuLYjx1dXBDWGpvOAHaE8&pid=Api&P=0&h=220',
    price: '$89.99',
    description: 'Memory foam orthopedic bed for large dogs'
  },
  {
    id: 2,
    title: 'Donut Cuddler',
    img: 'http://www.hkfoamus.com/images/thumbs/0000136_dog-bed.png',
    price: '$49.99',
    description: 'Soft, comfortable donut cuddler for small to medium dogs'
  },
  {
    id: 3,
    title: 'Elevated Dog Bed',
    img: 'https://snoozerpetproducts.com/wp-content/uploads/2014/03/Square_Dog_Bed_L_DarkChoc.png',
    price: '$39.99',
    description: 'Elevated cooling bed for outdoor use'
  },
  {
    id: 4,
    title: 'Luxury Dog Sofa',
    img: 'https://tse3.mm.bing.net/th?id=OIP.T-aMXUFiquELDU_8P_sn7wHaHa&pid=Api&P=0&h=220',
    price: '$129.99',
    description: 'Premium sofa-style bed for dogs'
  },
  {
    id: 5,
    title: 'Heated Dog Bed',
    img: 'https://tse3.mm.bing.net/th?id=OIP.9DntA--mwU_adCQLOuX56wHaEu&pid=Api&P=0&h=220',
    price: '$69.99',
    description: 'Self-warming bed for cold weather'
  }
];


const catBeds = [
  {
    id: 101,
    title: 'Cat Cave Bed',
    img: 'https://tse3.mm.bing.net/th?id=OIP.QtCixNY68nMwo1Gh_57JEAHaHa&pid=Api&P=0&h=220',
    price: '$29.99',
    description: 'Cozy cave bed for cats who like to hide'
  },
  {
    id: 102,
    title: 'Window Perch',
    img: 'https://wallpapers.com/images/hd/unicorn-themed-cat-bed-fo53w6fdl2hjkulr.png',
    price: '$24.99',
    description: 'Window-mounted perch for sunbathing cats'
  },
  {
    id: 103,
    title: 'Heated Cat Pad',
    img: 'https://png.pngtree.com/png-vector/20231115/ourmid/pngtree-dog-or-cat-bed-furniture-png-image_10597305.png',
    price: '$34.99',
    description: 'Self-heating pad for cats'
  },
  {
    id: 104,
    title: 'Cat Tree with Bed',
    img: 'https://tse4.mm.bing.net/th?id=OIP._E7VQwpTV_LA5k1qRS6xvgAAAA&pid=Api&P=0&h=220',
    price: '$79.99',
    description: 'Multi-level cat tree with built-in beds'
  },
  {
    id: 105,
    title: 'Luxury Cat Hammock',
    img: 'https://i.pinimg.com/originals/9f/32/18/9f3218d319b91a3621d92b6e28139005.png',
    price: '$19.99',
    description: 'Comfortable hammock for cats'
  }
];

const specialtyBeds = [
  {
    id: 201,
    title: 'Orthopedic Senior Bed',
    img: 'https://catorcat.com/wp-content/uploads/2021/02/green-cat-bed.png',
    price: '$99.99',
    description: 'Extra supportive bed for senior pets'
  },
  {
    id: 202,
    title: 'Anxiety Relief Bed',
    img: 'https://static.vecteezy.com/system/resources/thumbnails/014/166/556/small_2x/isometric-bedroom-3d-render-png.png',
    price: '$59.99',
    description: 'Calming bed for anxious pets'
  },
  {
    id: 203,
    title: 'Travel Pet Bed',
    img: 'https://amarpet-space.sgp1.digitaloceanspaces.com/production/d68a18275455ae3eaa2c291eebb46e6d/Bat-cat-bed.png',
    price: '$45.99',
    description: 'Portable bed for traveling with pets'
  },
  {
    id: 204,
    title: 'Outdoor Waterproof Bed',
    img: 'https://static.vecteezy.com/system/resources/thumbnails/009/585/332/small_2x/minimal-3d-illustration-of-wicker-cat-house-front-view-free-png.png',
    price: '$69.99',
    description: 'Waterproof bed for outdoor use'
  }
];

const Beds = () => {
  return (
    <>

      <section>
        <div className='p-10'>
          <div className='grid grid-cols-2 w-full rounded-4xl p-10 relative'>
            <div className='flex justify-center'>
              <img className='h-130 object-contain' src='https://images-na.ssl-images-amazon.com/images/I/71mCg4rfP0L._AC_SL1500_.jpg' alt="beds" />
            </div>

            <div className='flex flex-col justify-center'>
              <h1 className='text-6xl font-bold'>
                Comfortable Pet Beds
              </h1>

              <div className='pt-10'>
                <p className='block text-xl font-normal mt-2'>
                  Find the perfect bed for your furry friend. We offer a wide selection of comfortable, durable, and stylish beds for dogs and cats of all sizes.
                </p>

                <div className='flex items-center gap-2 mt-4'>
                  <div className='h-px bg-gray-400 flex-grow'></div>
                  <div className='flex gap-3'>
                    <a href="https://www.instagram.com" target='_blank'><FaInstagram className="text-[#575CEE] text-xl" /></a>
                    <a href="https://twitter.com" target='_blank'><FaTwitter className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.facebook.com" target='_blank'><FaFacebook className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.linkedin.com" target='_blank'><FaLinkedin className="text-[#575CEE] text-xl" /></a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>


      <section>
        <GenericProductSlider
          products={dogBeds}
          title="Dog Beds"
          slidesToShow={3}
          autoplaySpeed={4000}
        />
      </section>


      <section>
        <GenericProductSlider
          products={catBeds}
          title="Cat Beds"
          slidesToShow={3}
          autoplaySpeed={4000}
        />
      </section>

      <section className="mb-10">
        <GenericProductSlider
          products={specialtyBeds}
          title="Specialty Beds"
          slidesToShow={4}
          autoplay={false}
          className="bg-gray-50 py-10 rounded-xl"
        />
      </section>
    </>
  )
}

export default Beds
