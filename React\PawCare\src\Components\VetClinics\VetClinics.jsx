import React from 'react'
import heropic from '../../assets/imgs/heropic.png'
import VetCard from '../ClinicCard/ClinicCard'
import AppointmentModal from './AppointmentModal'
import { FaStethoscope, FaClinicMedical, FaUserMd, FaPaw, FaCalendarAlt } from 'react-icons/fa'

const VetClinics = () => {
  return (
    <>

      <div >
        <section>
          <div className='p-10'>
            <div className='grid grid-cols-2 w-full rounded-4xl p-10 relative bg-white shadow-lg border border-gray-100'>
              <div className='flex flex-col justify-center pr-8'>
                <div className="inline-block bg-green-100 text-green-600 px-4 py-1 rounded-full text-sm font-medium mb-4 flex items-center">
                  <FaStethoscope className="mr-2" /> Professional Veterinary Care
                </div>
                <h1 className='text-6xl font-bold text-gray-800 leading-tight'>
                  culpa qui officia deserunt
                </h1>

                <div className='pt-6'>
                  <span className='block text-xl font-normal text-gray-600'>
                    <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur Excepteur sint.</p>
                  </span>
                </div>

                <div className='flex gap-5 mt-8'>
                  <button className='mt-3 bg-green-500 text-white px-6 py-3 rounded-full hover:scale-105 transition duration-300 transform hover:shadow-lg shadow-green-200 font-medium flex items-center'>
                    <FaCalendarAlt className="mr-2" /> Ask Anything
                  </button>
                  <button className='mt-3 border-2 border-green-500 text-green-600 px-6 py-3 rounded-full hover:scale-105 transition duration-300 transform hover:shadow-lg shadow-green-100 font-medium flex items-center'>
                    <FaUserMd className="mr-2" /> Learn more
                  </button>
                </div>
              </div>

              <div className='flex justify-center items-center'>
                <div className="relative">
                  <div className="absolute -inset-4 bg-green-100 rounded-full opacity-50 blur-xl"></div>
                  <img
                    className='h-100 object-contain relative rounded-3xl shadow-lg hover:scale-105 transition-transform duration-500'
                    src={heropic}
                    alt="Veterinarian with dog"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>


      <section className="py-16 bg-white">
        <div className="container mx-auto px-10 text-center max-w-4xl">
          <h2 className="text-3xl font-bold mb-8 text-gray-800">Our Veterinary Services</h2>
          <p className="text-xl text-gray-600 mb-10 leading-relaxed">
            We provide comprehensive veterinary care for your beloved pets. Our experienced veterinarians
            are dedicated to keeping your pets healthy and happy.
          </p>

          <div className="grid grid-cols-3 gap-8 mt-12">
            <div className="flex flex-col items-center p-6 rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
              <div className="bg-green-100 p-4 rounded-full text-green-600 mb-4">
                <FaStethoscope size={24} />
              </div>
              <h3 className="font-bold text-lg mb-2">Wellness Exams</h3>
              <p className="text-gray-600 text-sm">Comprehensive health checks for your pets</p>
            </div>

            <div className="flex flex-col items-center p-6 rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
              <div className="bg-green-100 p-4 rounded-full text-green-600 mb-4">
                <FaUserMd size={24} />
              </div>
              <h3 className="font-bold text-lg mb-2">Surgery</h3>
              <p className="text-gray-600 text-sm">Advanced surgical procedures by specialists</p>
            </div>

            <div className="flex flex-col items-center p-6 rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
              <div className="bg-green-100 p-4 rounded-full text-green-600 mb-4">
                <FaPaw size={24} />
              </div>
              <h3 className="font-bold text-lg mb-2">Dental Care</h3>
              <p className="text-gray-600 text-sm">Complete dental services for optimal health</p>
            </div>
          </div>
        </div>
      </section>


      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-800">Our Clinic Locations</h2>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Find a veterinary clinic near you. Our network of clinics provides exceptional care
              for your pets with convenient locations and hours.
            </p>
          </div>

          <div className='w-full flex justify-center gap-6 flex-wrap'>
            <VetCard img="https://images.pexels.com/photos/668298/pexels-photo-668298.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" />
            <VetCard img="https://images.pexels.com/photos/668298/pexels-photo-668298.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" />
            <VetCard img="https://images.pexels.com/photos/668298/pexels-photo-668298.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" />
            <VetCard img="https://images.pexels.com/photos/668298/pexels-photo-668298.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"/>
            <VetCard img="https://images.pexels.com/photos/668298/pexels-photo-668298.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"/>
          </div>
        </div>
      </section>


      <section className="py-16 bg-white">
        <div className="container mx-auto px-10 max-w-5xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-800">What Pet Owners Say</h2>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Hear from pet owners who trust our veterinary clinics for their beloved companions.
            </p>
          </div>

          <div className="grid grid-cols-2 gap-8">
            <div className="bg-gray-50 p-8 rounded-xl shadow-sm">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center text-green-600 mr-4">
                  <FaPaw />
                </div>
                <div>
                  <h4 className="font-bold">Sarah Johnson</h4>
                  <p className="text-sm text-gray-500">Dog Owner</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "The staff at PawCare Veterinary Clinic are amazing! They took such good care of my dog Max during his recent surgery. I couldn't be more grateful."
              </p>
            </div>

            <div className="bg-gray-50 p-8 rounded-xl shadow-sm">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center text-green-600 mr-4">
                  <FaPaw />
                </div>
                <div>
                  <h4 className="font-bold">Michael Thompson</h4>
                  <p className="text-sm text-gray-500">Cat Owner</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "I've been bringing my cats to PawCare for years. The veterinarians are knowledgeable, caring, and always take the time to answer all my questions."
              </p>
            </div>
          </div>
        </div>
      </section>


      <section className="py-16 bg-green-600 text-white">
        <div className="container mx-auto px-10 text-center">
          <h2 className="text-3xl font-bold mb-4">Schedule an Appointment Today</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Your pet's health is our priority. Book an appointment with one of our experienced veterinarians.
          </p>
          <div className="flex justify-center gap-4">
            <AppointmentModal
              clinicName="PawCare Veterinary Clinic"
              triggerButton={{
                className: "bg-white text-green-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition duration-300 shadow-md flex items-center",
                text: <>
                  <FaCalendarAlt className="mr-2" /> Book Appointment
                </>
              }}
            />
            <button className="bg-transparent text-white border-2 border-white px-8 py-3 rounded-full font-semibold hover:bg-green-700 transition duration-300 flex items-center">
              <FaClinicMedical className="mr-2" /> Find a Clinic
            </button>
          </div>
        </div>
      </section>
    </>
  )
}

export default VetClinics
