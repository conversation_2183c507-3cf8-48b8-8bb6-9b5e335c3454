import React from 'react';
import { Link } from 'react-router-dom';
import { FaPaw, FaHome, FaArrowLeft } from 'react-icons/fa';

const PageNotFound = () => {
  return (
    <div className="pb-10 min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
      <div className="max-w-lg w-full text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="relative">
            <h1 className="text-9xl font-bold text-gray-200 select-none">404</h1>
            <div className="absolute inset-0 flex items-center justify-center">
              <FaPaw className="text-6xl text-[#575CEE] animate-bounce" />
            </div>
          </div>
        </div>

        {/* Error Message */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            Oops! Page Not Found
          </h2>
          <p className="text-gray-600 text-lg mb-2">
            The page you're looking for seems to have wandered off like a curious pet.
          </p>
          <p className="text-gray-500">
            Don't worry, we'll help you find your way back home!
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            to="/home"
            className="flex items-center gap-2 bg-[#575CEE] text-white px-6 py-3 rounded-lg hover:bg-[#4a4fd1] transition-colors duration-300 shadow-md"
          >
            <FaHome />
            Go Home
          </Link>

          <button
            onClick={() => window.history.back()}
            className="flex items-center gap-2 bg-white text-[#575CEE] border-2 border-[#575CEE] px-6 py-3 rounded-lg hover:bg-[#575CEE] hover:text-white transition-colors duration-300 shadow-md"
          >
            <FaArrowLeft />
            Go Back
          </button>
        </div>

        {/* Additional Help */}
        <div className="mt-12 p-6 bg-white rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">
            Looking for something specific?
          </h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <Link
              to="/about"
              className="text-[#575CEE] hover:underline"
            >
              Shop Products
            </Link>
            <Link
              to="/vet-clinics"
              className="text-[#575CEE] hover:underline"
            >
              Vet Clinics
            </Link>
            <Link
              to="/shelters"
              className="text-[#575CEE] hover:underline"
            >
              Pet Shelters
            </Link>
            <Link
              to="/cart"
              className="text-[#575CEE] hover:underline"
            >
              Shopping Cart
            </Link>
          </div>
        </div>

        {/* Fun Pet Fact */}
        <div className="mt-8  p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
          <p className="text-sm text-yellow-800">
            <strong>Fun Fact:</strong> Did you know that dogs can learn over 150 words?
            Unlike this page, they're great at following directions!
          </p>
        </div>
      </div>
    </div>
  );
};

export default PageNotFound;
