import React from 'react'
import { Fa<PERSON><PERSON><PERSON>ram, FaTwitter, FaFacebook, FaLinkedin } from 'react-icons/fa'
import GenericProductSlider from '../Slider/GenericProductSlider'

const Bath = () => {

    const dogBath = [
        { id: 1, title: 'Dog Bath', img: 'https://shop.welovedoodles.com/cdn/shop/files/206246_SENSITIVESKINSHAMPFront.png?v=1712797056', price: '$100', description: 'Premium dog bath for your furry friend' },
       { id: 2, title: 'Dog Bath', img:'https://i5.walmartimages.com/asr/1a1c1a99-55bc-43e4-a643-463bbfe68fed_1.d5ebcb72040fbf562f1a75e632be559e.png', price: '$100', description: 'Premium dog bath for your furry friend' },
       { id: 3, title: 'Dog Bath', img:'https://dogtime.com/wp-content/uploads/sites/12/2024/06/wags-wiggles_dog_shampoo.png', price: '$100', description: 'Premium dog bath for your furry friend' },
       { id: 4, title: 'Dog BBath', img:'https://cdn.store-assets.com/s/159919/i/2697896.png?width=1024', price: '$100', description: 'Premium dog bath for your furry friend' },
       { id: 5, title: 'Dog Bath', img:'https://i5.walmartimages.com/asr/1e51203f-dca5-4afe-9a88-923372072380.e9cf582cac1f326dd325906069b69109.png?odnWidth=1000&odnHeight=1000&odnBg=ffffff', price: '$100', description: 'Premium dog bath for your furry friend' },
    ]

const catBath = [
    { id: 11, title: 'Cat Bath', img: 'https://luxo-pet.com/wp-content/uploads/2023/05/Beep-CatCare-Shampo.png', price: '$100', description: 'Premium cat bath for your furry friend' },
    { id: 12, title: 'Cat Bath', img: 'https://miraclecarepet.com/wp-content/uploads/2020/04/11004_NatFleaTickShampooCat_16ozF.png', price: '$100', description: 'Premium cat bath for your furry friend' },
    { id: 13, title: 'Cat Bath', img: 'https://stratfordrx.com/wp-content/uploads/2022/12/SP1068-Tearless-Shampoo-16oz.png', price: '$100', description: 'Premium cat bath for your furry friend' },
    { id: 14, title: 'Cat Bath', img: 'https://i5.walmartimages.com/asr/7e2f405f-4ef7-4a28-ad49-4a94499a2133_1.672b4e481baa094e0b786eda4124a3cb.png', price: '$100', description: 'Premium cat bath for your furry friend' },
    { id: 15, title: 'Cat Bath', img: 'https://petpro.tropiclean.com/wp-content/uploads/2019/04/TC_OXY_Product-Photo_Medicated-Oatmeal-Shampoo_20oz_FRONT-683x1024.png', price: '$100', description: 'Premium cat bath for your furry friend' },

]

const specialtyBath = [

    { id: 21, title: 'Specialty Bath', img: 'https://zoomagazintiger.com/media/7/16175.png', price: '$100', description: 'Premium bath for your furry friend' },
    { id: 22, title: 'Specialty Bath', img: 'https://www.doggyfriend.com/assets/order_images/1284451(3).png', price: '$100', description: 'Premium bath for your furry friend' },
    { id: 23, title: 'Specialty Bath', img: 'https://sergeants.com/wp-content/uploads/2021/02/00073091001027-P000181-1_FRNT.png', price: '$100', description: 'Premium bath for your furry friend' },
    { id: 24, title: 'Specialty Bath', img: 'https://www.petfoodnmore.com/wp-content/uploads/2019/06/zodiac-flea-tic-shampoo-240ml-lg.png', price: '$100', description: 'Premium bath for your furry friend' },
    { id: 25, title: 'Specialty Bath', img: 'https://bluegold.garden/wp-content/uploads/2013/06/Dog-Shampoo-Mockup.png', price: '$100', description: 'Premium bath for your furry friend' },
]
  return (
    <>
      <section>
        <div className='p-10'>
          <div className='grid grid-cols-2 w-full rounded-4xl p-10 relative'>
            <div className='flex justify-center'>
              <img className='h-130 object-contain' src='https://bluegold.garden/wp-content/uploads/2013/06/Dog-Shampoo-Mockup.png' alt="bath" />
            </div>

            <div className='flex flex-col justify-center'>
              <h1 className='text-6xl font-bold'>
                New Grooming products for your pets
              </h1>

              <div className='pt-10'>
                <p className='block text-xl font-normal mt-2'>
                  Find the perfect bath products for your furry friend. We offer a wide selection of bath products for dogs and cats of all sizes.
                </p>

                <div className='flex items-center gap-2 mt-4'>
                  <div className='h-px bg-gray-400 flex-grow'></div>
                  <div className='flex gap-3'>
                    <a href="https://www.instagram.com" target='_blank'><FaInstagram className="text-[#575CEE] text-xl" /></a>
                    <a href="https://twitter.com" target='_blank'><FaTwitter className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.facebook.com" target='_blank'><FaFacebook className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.linkedin.com" target='_blank'><FaLinkedin className="text-[#575CEE] text-xl" /></a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

       <section> <GenericProductSlider products={dogBath} title="Dog Bath"slidesToShow={3} autoplaySpeed={4000} /></section>

       <section> <GenericProductSlider products={catBath} title="Cat Bath"slidesToShow={3} autoplaySpeed={4000} /></section>

       <section> <GenericProductSlider products={specialtyBath} title="Specialty Bath"slidesToShow={4} autoplaySpeed={4000} /></section>
    </>
  )
}

export default Bath
