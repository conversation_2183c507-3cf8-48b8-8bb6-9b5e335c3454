import './App.css'
import FooterComponent from './Components/Footer/Footer.jsx'
import { BrowserRouter, Routes, Route } from 'react-router-dom'
import About from './Components/About/About.jsx'
import Contact from './Components/contact/Contact.jsx'
import Home from './Components/Home/Home.jsx'
import HeaderNav from './Components/Header/Header.jsx'

function App() {
  return (
    <>
      <div className="w-full">
        <BrowserRouter>
          <nav className='pt-5 pr-10 bg-[#575CEE] h-20'>
            <HeaderNav/>
            <Routes>
              <Route path='/' element={<Home/>}/>
              <Route path='/about' element={<About/>}/>
              <Route path='/contact' element={<Contact/>}/>
            </Routes>
          </nav>
          
          <footer className='pt-20'>
            <FooterComponent/>
          </footer>
        </BrowserRouter>
      </div>
    </>
  )
}

export default App
