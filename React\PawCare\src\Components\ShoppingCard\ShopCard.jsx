
import React from 'react'
import { FaShoppingCart } from 'react-icons/fa'
import { useCart } from '../../context/CartContext'

function ShopCard({id, title, img, price, description}) {
  const { addToCart } = useCart();

  // Add to cart function using the cart context
  const handleAddToCart = () => {
    addToCart({ id, title, img, price, description });
  };

  return (
    <div className="transition-all duration-300 hover:shadow-2xl hover:-translate-y-1">
      <div className='shadow-lg border border-gray-200 rounded-lg overflow-hidden flex flex-col h-full'>
        <div className="h-64 overflow-hidden bg-white flex items-center justify-center p-4">
          <img className='h-full object-contain' src={img} alt={title} />
        </div>
        <div className="p-4 flex flex-col flex-grow">
          <h3 className="font-bold text-lg mb-1">{title}</h3>
          {description && (
            <p className="text-gray-600 text-sm mb-2 flex-grow">{description}</p>
          )}
          <div className="flex justify-between items-center mt-2">
            <span className="font-bold text-[#575CEE]">{price}</span>
            <button
              onClick={handleAddToCart}
              className="bg-[#575CEE] text-white p-2 rounded-full hover:bg-[#4a4fd1] transition-colors"
              title="Add to cart"
            >
              <FaShoppingCart />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ShopCard
