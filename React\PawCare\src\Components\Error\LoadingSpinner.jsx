import React from 'react';
import { FaPaw } from 'react-icons/fa';

const LoadingSpinner = ({ message = "Loading..." }) => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        {/* Animated Paw Icons */}
        <div className="flex justify-center space-x-2 mb-6">
          <FaPaw className="text-[#575CEE] text-2xl animate-bounce" style={{ animationDelay: '0ms' }} />
          <FaPaw className="text-[#575CEE] text-2xl animate-bounce" style={{ animationDelay: '150ms' }} />
          <FaPaw className="text-[#575CEE] text-2xl animate-bounce" style={{ animationDelay: '300ms' }} />
        </div>
        
        {/* Loading Spinner */}
        <div className="relative mb-4">
          <div className="w-16 h-16 border-4 border-gray-200 border-t-[#575CEE] rounded-full animate-spin mx-auto"></div>
        </div>
        
        {/* Loading Message */}
        <p className="text-gray-600 text-lg font-medium">{message}</p>
        <p className="text-gray-400 text-sm mt-2">Please wait while we fetch your content...</p>
      </div>
    </div>
  );
};

export default LoadingSpinner;
