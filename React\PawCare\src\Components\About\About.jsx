import React from 'react'
import catfood  from './../../assets/imgs/catfood.png'
import safe from './../../assets/imgs/safe.png'
import ShopCard from '../ShoppingCard/ShopCard'
import shopcat from './../../assets/imgs/Shop/shopcat.png'
import shopdog from './../../assets/imgs/Shop/shopdog.png'
import shopsome from './../../assets/imgs/Shop/shopsome.png'
import shopani from './../../assets/imgs/Shop/shopani.png'
import shopmore from './../../assets/imgs/Shop/shopmore.png'
import el1 from './../../assets/imgs/CircularAvatar/el1.png'
import el2 from './../../assets/imgs/CircularAvatar/el2.png'
import el3 from './../../assets/imgs/CircularAvatar/el3.png'
import el4 from './../../assets/imgs/CircularAvatar/el4.png'
import el5 from './../../assets/imgs/CircularAvatar/el5.png'
import el6 from './../../assets/imgs/CircularAvatar/el6.png'
import divimg from './../../assets/imgs/divimg.png'
import { Link } from 'react-router-dom'

const About = () => {
  return (
   <>
     <section>
           <div className='p-10'>
             <div className='grid grid-cols-3 bg-[#575CEE] w-full rounded-4xl p-10 relative'>
               <div className='flex  flex-col justify-center'>
                 <h1 className='text-6xl font-bold text-white '>
                 A Gift for you

                 </h1>
                 <span className='mt-3 block text-white text-xl text-md font-normal'>
                     <p> Enjoy <span className='font-bold text-xl text-black'>15%</span> of money. <br /> Food that is healthy for your kitty</p>
                   </span>

               </div>

            <div className=' flex  flex-col justify-center'>
             <img className=' h-16 object-contain' src={safe} alt="" />
            </div>

               <div className='flex justify-center'>
                 <img className='h-64 object-contain hover:scale-105 transition-transform duration-500' src={catfood} alt="cat food" />
               </div>
              <div className='flex justify-end w-full '>
             <Link to={'/CatShop'}><button className='mt-3 bg-white text-black px-4 py-2 rounded-full w-32 hover:scale-105 transition duration-300 transform hover:shadow-md shadow-[#333d29]'>Shop Now</button></Link>
              </div>
             </div>

           </div>

         </section>

         <section>
          <div className='p-10 text-center'>
            <h1 className='text-md font-semibold'>Top products of this week for the Dogs</h1>
          </div>

        <div className=' flex items-center justify-center '>
        <div className=' px-10 flex grid grid-cols-3 justify-between items-center gap-x-20 gap-y-10'>
         <ShopCard title={'Pet Toy for dogs'} img={shopcat} price={'$100'} />
         <ShopCard title={'Pet Toy for dogs'} img={shopdog} price={'$100'} />
         <ShopCard title={'Pet Toy for dogs'} img={shopsome} price={'$100'} />
         <ShopCard title={'Pet Toy for dogs'} img={shopmore} price={'$100'} />
         <ShopCard title={'Pet Toy for dogs'} img={shopsome} price={'$100'} />
         <ShopCard title={'Pet Toy for dogs'} img={shopani} price={'$100'} />
          </div>
        </div>

       <div className='w-full flex justify-center pt-10'>
     <Link to={'/shopview'}>  <button
       className='mt-3 border-2 border-purple-500 text-black px-4 py-2 rounded-full w-32 hover:scale-105 transition duration-300 transform hover:shadow-md shadow-[#333d29]'>View All</button></Link>
       </div>
         </section>

           <section>
           <div className='p-10 text-center'>
            <h1 className='text-md font-semibold'>Shop by collection</h1>

            <div className='pt-10'>
           <div className='gap-10 w-full flex justify-center'>
            <div>
           <Link to={'/beds'}> <img className='h-34 object-contain' src={el1} alt="" />
           <h1>Beds</h1></Link>
            </div>

           <div>
          <Link to={'/toys'}> <img className='h-34 object-contain' src={el2} alt="" />
           <h1>Toys</h1></Link>
           </div>
           <div>
          <Link to={'/bath'}> <img className='h-34 object-contain' src={el3} alt="" />
          <h1>Health and <br /> Bath</h1></Link>
           </div>
          <div>
          <Link to={'/food'}><img className='h-34 object-contain' src={el4} alt="" />
          <h1>Food</h1></Link>
          </div>
          <div>
          <Link to={'/treats'}><img className='h-34 object-contain' src={el5} alt="" />
          <h1>Treats</h1></Link>
          </div>
         <div>
         <Link to={'/furniture'}><img className='h-34 object-contain' src={el6} alt="" />
         <h1>Furniture</h1></Link>
         </div>
           </div>


            </div>
          </div>
           </section>

           <section className='p-10'>
            <div className=' w-full h-101 flex justify-start bg-blue-500 ml-3 mr-3 rounded-4xl '>
              <div className='relative  w-5/10 container cover-flow flex justify-center  rounded-tl-3xl rounded-bl-3xl'>

              </div>

              <img className='h-101 object-cover absolute' src={divimg} alt="" />

               <div className='w-5/10 flex flex-col justify-center gap-5 '>
               <h1 className='text-4xl font-bold text-white'>Made for all <br />
               lifs’s advantures.</h1>
               <h1 className='text-white'>
               Lorem ipsum dolor sit amet, <br /> consectetur adipiscing elit, <br /> sed do eiusmod tempor.
               </h1>

              <div className='flex justify-center w-full '>
              <button className=' mt-3 w-51 border-2 border-white text-white px-4 py-2 rounded-full w-32 hover:scale-105 transition duration-300 transform hover:shadow-md shadow-[#333d29]'>Shop Our  Collection</button>
              </div>
               </div>

            </div>
           </section>

           <section>
           <div className='p-10 text-center'>
            <h1 className='text-md font-semibold'>NEWSLETTER</h1>
          </div>
          <div className='p-5 text-center'>
          <h1 className='text-xl font-semibold'>Sign up and get up to <span className='font-bold text-xl text-red-500'>20% </span>off your <br /> first purchase</h1>
          <div className='pt-10 flex justify-center w-full '>
          <button className='mt-3 border-2 border-purple-500 text-black px-4 py-2 rounded-full w-32 hover:scale-105 transition duration-300 transform hover:shadow-md shadow-[#333d29]'>Sign Up</button>
          </div>
          </div>
           </section>

   </>
  )
}

export default About
