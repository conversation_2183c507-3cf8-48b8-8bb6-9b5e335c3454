import React from 'react';
import Slider from 'react-slick';
import ShopCard from '../ShoppingCard/ShopCard';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

// Import slick carousel CSS
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

// Custom arrow components
const PrevArrow = (props) => {
  const { className, style, onClick } = props;
  return (
    <div
      className={`${className} z-10`}
      style={{
        ...style,
        background: '#575CEE',
        borderRadius: '50%',
        width: '30px',
        height: '30px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        left: '-15px'
      }}
      onClick={onClick}
    >
      <FaChevronLeft className="text-white" />
    </div>
  );
};

const NextArrow = (props) => {
  const { className, style, onClick } = props;
  return (
    <div
      className={`${className} z-10`}
      style={{
        ...style,
        background: '#575CEE',
        borderRadius: '50%',
        width: '30px',
        height: '30px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        right: '-15px'
      }}
      onClick={onClick}
    >
      <FaChevronRight className="text-white" />
    </div>
  );
};

/**
 * GenericProductSlider - A reusable slider component for displaying products
 *
 * @param {Object} props - Component props
 * @param {Array} props.products - Array of product objects to display
 * @param {string} props.title - Title to display above the slider
 * @param {number} props.slidesToShow - Number of slides to show at once (default: 4)
 * @param {boolean} props.autoplay - Whether to autoplay the slider (default: true)
 * @param {number} props.autoplaySpeed - Speed of autoplay in ms (default: 3000)
 * @param {boolean} props.infinite - Whether the slider should loop infinitely (default: true)
 * @param {Object} props.customSettings - Additional settings to pass to react-slick
 * @param {string} props.className - Additional CSS classes for the container
 */
const GenericProductSlider = ({
  products,
  title,
  slidesToShow = 4,
  autoplay = true,
  autoplaySpeed = 3000,
  infinite = true,
  customSettings = {},
  className = ""
}) => {

  // Slider settings
  const settings = {
    dots: true,
    infinite: infinite,
    speed: 500,
    slidesToShow: slidesToShow,
    slidesToScroll: 1,
    autoplay: autoplay,
    autoplaySpeed: autoplaySpeed,
    prevArrow: <PrevArrow />,
    nextArrow: <NextArrow />,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: Math.min(3, slidesToShow),
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: Math.min(2, slidesToShow),
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1
        }
      }
    ],
    ...customSettings
  };

  return (
    <div className={`px-10 py-8 ${className}`}>
      {title && <h1 className='text-center text-3xl font-bold mb-8'>{title}</h1>}
      <div className="relative">
        <Slider {...settings}>
          {products.map((product) => (
            <div key={product.id} className="px-2">
              <ShopCard
                id={product.id}
                title={product.title}
                img={product.img}
                price={product.price}
                description={product.description}
              />
            </div>
          ))}
        </Slider>
      </div>
    </div>
  );
};

export default GenericProductSlider;
