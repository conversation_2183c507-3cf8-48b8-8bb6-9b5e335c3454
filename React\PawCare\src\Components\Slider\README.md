# Product Slider Component

A reusable slider component for displaying products in the PawCare application.

## Features

- Display products in a responsive slider
- Customizable number of slides to show
- Autoplay functionality with adjustable speed
- Custom navigation arrows
- Responsive design for different screen sizes
- Integration with cart functionality

## Usage

### Basic Usage

```jsx
import GenericProductSlider from '../Slider/GenericProductSlider';
import { newArrivals } from '../../data/productData';

// Basic usage with default settings
<GenericProductSlider 
  products={newArrivals} 
  title="New Arrivals" 
/>
```

### Customizing the Slider

```jsx
// Custom number of slides and no autoplay
<GenericProductSlider 
  products={petToys} 
  title="Pet Toys" 
  slidesToShow={3}
  autoplay={false}
/>

// Custom settings with faster autoplay
<GenericProductSlider 
  products={newArrivals} 
  title="Featured Products" 
  slidesToShow={5}
  autoplaySpeed={1500}
  customSettings={{
    centerMode: true,
    centerPadding: '60px',
  }}
/>

// No title and custom class
<GenericProductSlider 
  products={petToys.slice(0, 5)} 
  slidesToShow={2}
  className="bg-gray-100 rounded-xl"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| products | Array | required | Array of product objects to display |
| title | String | null | Title to display above the slider |
| slidesToShow | Number | 4 | Number of slides to show at once |
| autoplay | Boolean | true | Whether to autoplay the slider |
| autoplaySpeed | Number | 3000 | Speed of autoplay in ms |
| infinite | Boolean | true | Whether the slider should loop infinitely |
| customSettings | Object | {} | Additional settings to pass to react-slick |
| className | String | "" | Additional CSS classes for the container |

## Product Object Structure

Each product in the `products` array should have the following structure:

```js
{
  id: 1,                // Unique identifier
  title: 'Product Name', // Product title
  img: 'image-url.jpg',  // Product image URL
  price: '$100',         // Product price (as string with currency symbol)
  description: 'Product description' // Optional product description
}
```

## Example

See `ProductSliderExample.jsx` for a complete example of how to use this component with different configurations.

## Dependencies

- react-slick
- slick-carousel
- react-icons

Make sure these dependencies are installed in your project.
