import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalTrigger,
} from "../ui/animated-modal";
import { FaHandHoldingHeart, FaUser, FaEnvelope, FaQuestionCircle, FaCommentDots } from 'react-icons/fa';

const ContactModal = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Contact form submitted:', formData);
    alert('Thank you for your inquiry! We will get back to you soon.');
    // Reset form
    setFormData({
      name: '',
      email: '',
      subject: '',
      message: ''
    });
  };

  return (
    <Modal>
      <ModalTrigger className="bg-blue-500 text-white px-6 py-3 rounded-full hover:scale-105 transition duration-300 transform hover:shadow-lg shadow-blue-200 font-medium flex items-center">
        <FaHandHoldingHeart className="mr-2" /> Ask Anything
      </ModalTrigger>
      <ModalBody>
        <ModalContent className="max-h-[80vh] overflow-y-auto">
          <div className="text-center mb-4">
            <div className="bg-blue-100 p-3 rounded-full text-blue-600 mb-3 w-12 h-12 flex items-center justify-center mx-auto">
              <FaQuestionCircle size={20} />
            </div>
            <h4 className="text-xl font-bold text-gray-800 mb-2">
              Ask Us Anything
            </h4>
            <p className="text-gray-600 text-sm">
              Have questions about our shelter, adoption process, or need help? We're here to help!
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                <FaUser className="inline mr-1" />
                Your Name
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your full name"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                <FaEnvelope className="inline mr-1" />
                Email Address
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your email address"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                <FaQuestionCircle className="inline mr-1" />
                Subject
              </label>
              <select
                name="subject"
                value={formData.subject}
                onChange={handleInputChange}
                required
                className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select a topic</option>
                <option value="adoption">Adoption Process</option>
                <option value="volunteering">Volunteering Opportunities</option>
                <option value="donations">Donations & Support</option>
                <option value="animal-care">Animal Care Questions</option>
                <option value="visiting">Visiting the Shelter</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                <FaCommentDots className="inline mr-1" />
                Your Message
              </label>
              <textarea
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                required
                rows="3"
                className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="Tell us how we can help you..."
              />
            </div>
          </form>
        </ModalContent>
        <ModalFooter className="gap-3 p-3">
          <button
            type="button"
            className="px-3 py-1.5 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            type="submit"
            onClick={handleSubmit}
            className="px-3 py-1.5 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200 flex items-center"
          >
            <FaHandHoldingHeart className="mr-1" />
            Send Message
          </button>
        </ModalFooter>
      </ModalBody>
    </Modal>
  );
};

export default ContactModal;
