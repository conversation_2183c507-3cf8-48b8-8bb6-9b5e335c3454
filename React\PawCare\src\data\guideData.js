// Sample guide data for the slider
export const guideArticles = [
  {
    id: 1,
    title: 'The Queen',
    authorName: '<PERSON><PERSON>f<PERSON>',
    backgroundImage: 'https://images.pexels.com/photos/1314550/pexels-photo-1314550.jpeg?auto=compress&cs=tinysrgb&w=600'
  },
  {
    id: 2,
    title: '<PERSON>',
    authorName: '<PERSON>',
    backgroundImage: 'https://images.pexels.com/photos/31661820/pexels-photo-31661820/free-photo-of-majestic-gray-wolf-in-snowy-montana-wilderness.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  },
  {
    id: 3,
    title: '<PERSON> Training',
    authorName: '<PERSON>',
    backgroundImage: 'https://images.pexels.com/photos/106689/pexels-photo-106689.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  },
  {
    id: 4,
    title: '<PERSON> Behavior',
    authorName: '<PERSON>',
    backgroundImage: 'https://images.pexels.com/photos/326012/pexels-photo-326012.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  },
  {
    id: 5,
    title: 'Pet Nutrition',
    authorName: '<PERSON>',
    backgroundImage: 'https://images.pexels.com/photos/406014/pexels-photo-406014.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  },
  {
    id: 6,
    title: 'Grooming Tips',
    authorName: 'David Lee',
    backgroundImage: 'https://images.pexels.com/photos/1741205/pexels-photo-1741205.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  },
  {
    id: 7,
    title: 'Pet Health',
    authorName: 'Jessica Taylor',
    backgroundImage: 'https://images.pexels.com/photos/2253275/pexels-photo-2253275.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  },
  {
    id: 8,
    title: 'Adoption Guide',
    authorName: 'Robert Garcia',
    backgroundImage: 'https://images.pexels.com/photos/551628/pexels-photo-551628.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  }
];
