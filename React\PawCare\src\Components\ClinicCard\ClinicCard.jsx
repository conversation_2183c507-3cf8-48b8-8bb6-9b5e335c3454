import React from "react";
import AppointmentModal from '../VetClinics/AppointmentModal';

const VetCard = ({img}) => {
  return (
    <div className="bg-white shadow-lg rounded-2xl p-4 flex items-center  justify-between w-full  mx-auto mt-6">
      {/* Image */}
      <img
          src={img}
        alt="Veterinary Clinic"
        className="w-32 h-32 rounded-xl object-cover"
      />

      {/* Info */}
      <div className="flex-1 px-4">
        <h2 className="text-lg font-semibold">Veterinarian New Braunfels</h2>

        <div className="text-sm text-gray-600 mt-2 flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <span>📍</span>
            <span>1631 McQueeney Rd, New Braunfels, TX 78130</span>
          </div>
          <div className="flex items-center gap-2">
            <span>📞</span>
            <span>+1 2234454566</span>
          </div>
          <div className="flex items-center gap-2">
            <span>👨‍⚕️</span>
            <span>Dr. <PERSON></span>
          </div>
        </div>
      </div>

      {/* Timing & Days */}
      <div className="text-right flex flex-col justify-between h-full gap-2">
        <div className="text-sm">
          <div className="font-medium">8:00 AM - 7:00 PM</div>
          <div className="text-xs mt-1">
            <span className="text-black font-semibold">Sat </span>
            <span className="text-blue-600 font-semibold">Sun Mon Tu </span>
            <span className="text-black">Wed Thu </span>
            <span className="text-indigo-600 font-semibold">Fri</span>
          </div>
        </div>

        {/* Button */}
        <AppointmentModal
          clinicName="Veterinarian New Braunfels"
          triggerButton={{
            className: "bg-indigo-500 text-white py-1.5 px-4 rounded-full text-sm hover:bg-indigo-600 transition",
            text: "Book an Appointment"
          }}
        />
      </div>
    </div>
  );
};

export default VetCard;
