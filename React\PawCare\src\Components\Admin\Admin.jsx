import React, { useState } from 'react';
import {
  FaClinicMedical,
  FaChartPie,
  FaUsers,
  FaPaw,
  FaShoppingCart,
  FaCalendarAlt,
  FaCog,
  FaBell,
  FaSearch,
  FaSignOutAlt,
  FaTachometerAlt,
  FaBoxOpen,
  FaMoneyBillWave
} from 'react-icons/fa';

const SystemAdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [notifications] = useState(0);

  // Mock data for dashboard
  const stats = [
    { id: 1, title: 'Total Users', value: '1,285', icon: <FaUsers />, change: '+12%', color: 'bg-blue-500' },
    { id: 2, title: 'Total Products', value: '356', icon: <FaBoxOpen />, change: '+5%', color: 'bg-green-500' },
    { id: 3, title: 'Total Orders', value: '843', icon: <FaShoppingCart />, change: '+18%', color: 'bg-purple-500' },
    { id: 4, title: 'Revenue', value: '$24,500', icon: <FaMoneyBillWave />, change: '+8%', color: 'bg-yellow-500' },
  ];

  // multer / cloudnary

  const recentOrders = [
    { id: '#ORD-001', customer: 'John Doe', date: '2023-06-15', status: 'Completed', amount: '$120.00' },
    { id: '#ORD-002', customer: 'Jane Smith', date: '2023-06-14', status: 'Processing', amount: '$85.50' },
    { id: '#ORD-003', customer: 'Robert Johnson', date: '2023-06-14', status: 'Completed', amount: '$220.75' },
    { id: '#ORD-004', customer: 'Emily Davis', date: '2023-06-13', status: 'Pending', amount: '$65.25' },
    { id: '#ORD-005', customer: 'Michael Brown', date: '2023-06-12', status: 'Completed', amount: '$175.00' },
  ];

  const recentUsers = [
    { id: 1, name: 'Sarah Wilson', email: '<EMAIL>', joined: '2023-06-15', type: 'Customer' },
    { id: 2, name: 'David Miller', email: '<EMAIL>', joined: '2023-06-14', type: 'Customer' },
    { id: 3, name: 'Lisa Anderson', email: '<EMAIL>', joined: '2023-06-13', type: 'Vendor' },
    { id: 4, name: 'James Taylor', email: '<EMAIL>', joined: '2023-06-12', type: 'Customer' },
  ];

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const getStatusColor = (status) => {
    switch(status) {
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Processing': return 'bg-blue-100 text-blue-800';
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-20'} bg-[#575CEE] text-white transition-all duration-300 ease-in-out`}>
        <div className="p-4 flex items-center justify-between">
          <div className={`flex items-center ${!sidebarOpen && 'justify-center w-full'}`}>

            {sidebarOpen && <span className="ml-2 text-xl font-semibold">PawCare Admin</span>}
          </div>
          <button onClick={toggleSidebar} className="text-white focus:outline-none">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {sidebarOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
              )}
            </svg>
          </button>
        </div>
        <nav className="mt-5">
          <ul>
            <li className="mb-2">
              <button
                onClick={() => setActiveTab('dashboard')}
                className={`flex items-center p-3 w-full ${activeTab === 'dashboard' ? 'bg-indigo-700' : 'hover:bg-indigo-600'} rounded-lg transition-colors`}
              >
                <FaTachometerAlt className="text-xl" />
                {sidebarOpen && <span className="ml-3">Dashboard</span>}
              </button>
            </li>
            <li className="mb-2">
              <button
                onClick={() => setActiveTab('users')}
                className={`flex items-center p-3 w-full ${activeTab === 'users' ? 'bg-indigo-700' : 'hover:bg-indigo-600'} rounded-lg transition-colors`}
              >
                <FaUsers className="text-xl" />
                {sidebarOpen && <span className="ml-3">Users</span>}
              </button>
            </li>
            <li className="mb-2">
              <button
                onClick={() => setActiveTab('products')}
                className={`flex items-center p-3 w-full ${activeTab === 'products' ? 'bg-indigo-700' : 'hover:bg-indigo-600'} rounded-lg transition-colors`}
              >
                <FaBoxOpen className="text-xl" />
                {sidebarOpen && <span className="ml-3">Products</span>}
              </button>
            </li>
            <li className="mb-2">
              <button
                onClick={() => setActiveTab('orders')}
                className={`flex items-center p-3 w-full ${activeTab === 'orders' ? 'bg-indigo-700' : 'hover:bg-indigo-600'} rounded-lg transition-colors`}
              >
                <FaShoppingCart className="text-xl" />
                {sidebarOpen && <span className="ml-3">Orders</span>}
              </button>
            </li>
            <li className="mb-2">
              <button
                onClick={() => setActiveTab('appointments')}
                className={`flex items-center p-3 w-full ${activeTab === 'appointments' ? 'bg-indigo-700' : 'hover:bg-indigo-600'} rounded-lg transition-colors`}
              >
                <FaCalendarAlt className="text-xl" />
                {sidebarOpen && <span className="ml-3">Appointments</span>}
              </button>
            </li>
            <li className="mb-2">
              <button
                onClick={() => setActiveTab('clinics')}
                className={`flex items-center p-3 w-full ${activeTab === 'clinics' ? 'bg-indigo-700' : 'hover:bg-indigo-600'} rounded-lg transition-colors`}
              >
                <FaClinicMedical className="text-xl" />
                {sidebarOpen && <span className="ml-3">Vet Clinics</span>}
              </button>
            </li>
            <li className="mb-2">
              <button
                onClick={() => setActiveTab('reports')}
                className={`flex items-center p-3 w-full ${activeTab === 'reports' ? 'bg-indigo-700' : 'hover:bg-indigo-600'} rounded-lg transition-colors`}
              >
                <FaChartPie className="text-xl" />
                {sidebarOpen && <span className="ml-3">Reports</span>}
              </button>
            </li>
            <li className="mb-2">
              <button
                onClick={() => setActiveTab('settings')}
                className={`flex items-center p-3 w-full ${activeTab === 'settings' ? 'bg-indigo-700' : 'hover:bg-indigo-600'} rounded-lg transition-colors`}
              >
                <FaCog className="text-xl" />
                {sidebarOpen && <span className="ml-3">Settings</span>}
              </button>
            </li>
          </ul>
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-sm">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center">
              <div className="relative">
                <span className="absolute inset-y-0 left-0 pl-3 flex items-center">
                  <FaSearch className="text-gray-400" />
                </span>
                <input
                  type="text"
                  placeholder="Search..."
                  className="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <button className="text-gray-500 hover:text-gray-700 focus:outline-none">
                  <FaBell className="text-xl" />
                  {notifications > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs">
                      {notifications}
                    </span>
                  )}
                </button>
              </div>
              <div className="flex items-center">
                <img
                  src="https://randomuser.me/api/portraits/men/1.jpg"
                  alt="Admin"
                  className="w-8 h-8 rounded-full"
                />
                <div className="ml-2">
                  <p className="text-sm font-medium text-gray-700">Admin User</p>
                  <p className="text-xs text-gray-500">System Administrator</p>
                </div>
              </div>
              <button className="text-gray-500 hover:text-gray-700 focus:outline-none">
                <FaSignOutAlt className="text-xl" />
              </button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-4">
          {activeTab === 'dashboard' && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <h1 className="text-2xl font-semibold text-gray-800">Dashboard</h1>
                <div>
                  <button className="bg-[#575CEE] text-white px-4 py-2 rounded-lg hover:bg-[#4a4fd1] transition-colors">
                    Generate Report
                  </button>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h2 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <button className="bg-blue-50 hover:bg-blue-100 p-4 rounded-lg flex flex-col items-center justify-center transition-colors">
                    <FaBoxOpen className="text-blue-500 text-2xl mb-2" />
                    <span className="text-sm font-medium text-gray-700">Add Product</span>
                  </button>
                  <button className="bg-green-50 hover:bg-green-100 p-4 rounded-lg flex flex-col items-center justify-center transition-colors">
                    <FaUsers className="text-green-500 text-2xl mb-2" />
                    <span className="text-sm font-medium text-gray-700">Add User</span>
                  </button>
                  <button className="bg-purple-50 hover:bg-purple-100 p-4 rounded-lg flex flex-col items-center justify-center transition-colors">
                    <FaCalendarAlt className="text-purple-500 text-2xl mb-2" />
                    <span className="text-sm font-medium text-gray-700">Schedule</span>
                  </button>
                  <button className="bg-yellow-50 hover:bg-yellow-100 p-4 rounded-lg flex flex-col items-center justify-center transition-colors">
                    <FaChartPie className="text-yellow-500 text-2xl mb-2" />
                    <span className="text-sm font-medium text-gray-700">Reports</span>
                  </button>
                </div>
              </div>

              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                {stats.map((stat) => (
                  <div key={stat.id} className="bg-white rounded-lg shadow-sm p-6 flex items-start">
                    <div className={`${stat.color} text-white p-3 rounded-lg`}>
                      {stat.icon}
                    </div>
                    <div className="ml-4">
                      <p className="text-sm text-gray-500">{stat.title}</p>
                      <h3 className="text-xl font-semibold text-gray-800">{stat.value}</h3>
                      <p className="text-xs text-green-500">{stat.change} from last month</p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Charts Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold text-gray-800">Revenue Overview</h2>
                    <select className="text-sm border rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-[#575CEE]">
                      <option>Last 7 Days</option>
                      <option>Last 30 Days</option>
                      <option>Last 90 Days</option>
                    </select>
                  </div>
                  <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                    <p className="text-gray-500">Revenue Chart Placeholder</p>
                  </div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold text-gray-800">Sales Analytics</h2>
                    <select className="text-sm border rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-[#575CEE]">
                      <option>Last 7 Days</option>
                      <option>Last 30 Days</option>
                      <option>Last 90 Days</option>
                    </select>
                  </div>
                  <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                    <p className="text-gray-500">Sales Chart Placeholder</p>
                  </div>
                </div>
              </div>

              {/* Recent Orders */}
              <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-800">Recent Orders</h2>
                  <button className="text-[#575CEE] hover:underline text-sm focus:outline-none">
                    View All
                  </button>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Order ID
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Customer
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {recentOrders.map((order) => (
                        <tr key={order.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[#575CEE]">
                            {order.id}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                            {order.customer}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {order.date}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(order.status)}`}>
                              {order.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                            {order.amount}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Recent Users */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-800">Recent Users</h2>
                  <button className="text-[#575CEE] hover:underline text-sm focus:outline-none">
                    View All
                  </button>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Email
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Joined
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {recentUsers.map((user) => (
                        <tr key={user.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                {user.name.charAt(0)}
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">{user.name}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {user.email}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {user.joined}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                              {user.type}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <button className="text-[#575CEE] hover:underline mr-3">Edit</button>
                            <button className="text-red-500 hover:underline">Delete</button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab !== 'dashboard' && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h1 className="text-2xl font-semibold text-gray-800 mb-6">{activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}</h1>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <p className="text-gray-500">{activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} content will be displayed here</p>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default SystemAdminDashboard;