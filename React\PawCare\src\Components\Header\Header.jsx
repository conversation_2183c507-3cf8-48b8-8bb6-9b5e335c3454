import React, { useState, useEffect } from 'react'
import { FaPaw, FaUserCircle, FaSignOutAlt } from "react-icons/fa";
import { BookingModal } from '../../Components/BookingModal/BookingModal';
import CartIcon from '../../Components/Cart/CartIcon';
import { Link, useNavigate, useLocation } from 'react-router-dom'

function HeaderNav() {
  const navigate = useNavigate();
  const location = useLocation();
  const [user, setUser] = useState(null);


  const isAdminPage = location.pathname === '/admin';

  useEffect(() => {
    const isAuthenticated = localStorage.getItem('isAuthenticated');
    if (isAuthenticated != null) {
      setUser({
        name: localStorage.getItem('userName'),
        email: localStorage.getItem('userEmail')
      });
    }
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('userName');
    localStorage.removeItem('userEmail');
    setUser(null);


  };

  return (
    <>
      <section className='flex justify-between w-full h-8 items-center text-center text-white mx-4'>
        <div>
          <FaPaw className='text-2xl text-white' />
        </div>

        {!isAdminPage ? (
          <div className='ml-2'>
            <ul className='flex justify-between gap-6'>
              <li>
                <Link
                  to={'/home'}
                  className={`nav-link text-sm relative px-1 py-2 group ${
                    location.pathname === '/home'
                      ? 'text-yellow-300 font-medium'
                      : 'text-white hover:text-white/90'
                  }`}
                >
                  Home
                  <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-white transform origin-left transition-transform duration-300 ${
                    location.pathname === '/home'
                      ? 'scale-x-100'
                      : 'scale-x-0 group-hover:scale-x-100'
                  }`}></span>
                </Link>
              </li>
              <li>
                <Link
                  to={'/about'}
                  className={`nav-link text-sm relative px-1 py-2 group ${
                    location.pathname === '/about'
                      ? 'text-yellow-300 font-medium'
                      : 'text-white hover:text-white/90'
                  }`}
                >
                  Shop
                  <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-white transform origin-left transition-transform duration-300 ${
                    location.pathname === '/about'
                      ? 'scale-x-100'
                      : 'scale-x-0 group-hover:scale-x-100'
                  }`}></span>
                </Link>
              </li>
              <li>
                <Link
                  to={'/vet-clinics'}
                  className={`nav-link text-sm relative px-1 py-2 group ${
                    location.pathname === '/vet-clinics'
                      ? 'text-yellow-300 font-medium'
                      : 'text-white hover:text-white/90'
                  }`}
                >
                  Vet-Clinics
                  <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-white transform origin-left transition-transform duration-300 ${
                    location.pathname === '/vet-clinics'
                      ? 'scale-x-100'
                      : 'scale-x-0 group-hover:scale-x-100'
                  }`}></span>
                </Link>
              </li>
              <li>
                <Link
                  to={'/shelters'}
                  className={`nav-link text-sm relative px-1 py-2 group ${
                    location.pathname === '/shelters'
                      ? 'text-yellow-300 font-medium'
                      : 'text-white hover:text-white/90'
                  }`}
                >
                  Shelters
                  <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-white transform origin-left transition-transform duration-300 ${
                    location.pathname === '/shelters'
                      ? 'scale-x-100'
                      : 'scale-x-0 group-hover:scale-x-100'
                  }`}></span>
                </Link>
              </li>
            </ul>
          </div>
        ) : (
          <div className='ml-2 flex items-center gap-4'>
            <h1 className='text-white text-lg font-semibold'>Admin Dashboard</h1>
            <Link
              to={'/home'}
              className="text-white text-sm hover:text-yellow-300 transition-colors duration-300 border border-white/30 px-3 py-1 rounded-md hover:border-yellow-300"
            >
              Back to Home
            </Link>
          </div>
        )}

        <div className='flex gap-4 items-center'>
          {user ? (
            <>
              {!isAdminPage && <CartIcon />}
              {!isAdminPage && <BookingModal />}
              <div className="flex items-center gap-2">
                <FaUserCircle className="text-white text-lg" />
                <span className="text-white text-sm">{user.name || user.email}</span>
              </div>

              {/* {!isAdminPage && (
                <Link
                  to={'/admin'}
                  className="text-white text-sm hover:text-yellow-300 transition-colors duration-300"
                >
                  Admin
                </Link>
              )} */}

              <button
                onClick={handleLogout}
                className="flex items-center gap-1 text-white text-sm hover:text-gray-200"
              >
                <FaSignOutAlt className="text-white" />
                Logout
              </button>
            </>
          ) : (
            <>
              <Link
                to={'/login'}
                className={`nav-link text-sm relative px-1 py-2 group ${
                  location.pathname === '/login'
                    ? 'text-yellow-300 font-medium'
                    : 'text-white hover:text-white/90'
                }`}
              >
                Login
                <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-white transform origin-left transition-transform duration-300 ${
                  location.pathname === '/login'
                    ? 'scale-x-100'
                    : 'scale-x-0 group-hover:scale-x-100'
                }`}></span>
              </Link>
              <Link
                to={'/signup'}
                className="nav-link bg-white text-[#575CEE] text-sm px-3 py-1 rounded-md hover:bg-gray-100 transition-colors duration-300"
              >
                Sign Up
              </Link>
            </>
          )}

        </div>
      </section>
    </>
  )
}

export default HeaderNav
