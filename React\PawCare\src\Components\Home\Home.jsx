
import anibg from '../../assets/imgs/anibg.png'
import safe from '../../assets/imgs/safe.png'
import PetCategories from '../../Components/Categories/Categories.jsx'
import GenericProductSlider from '../../Components/Slider/GenericProductSlider'
import GuideSlider from '../../Components/Slider/GuideSlider'
import { newArrivals, petToys } from '../../data/productData'
import { guideArticles } from '../../data/guideData'
import { FaPaw, FaShippingFast, FaHeadset, FaShieldAlt } from 'react-icons/fa'
import { Link } from 'react-router-dom'

const Home = () => {
  return (
    <div >

      <section className="relative overflow-hidden">
        <div className="absolute inset-0 "></div>
        <div className='p-10'>
          <div className='grid grid-cols-3 bg-[#575CEE] w-full rounded-4xl p-10 relative shadow-xl'>
            <div className='flex flex-col justify-center'>
              <h1 className='text-6xl font-bold text-white drop-shadow-md'>
                Paw Care
              </h1>
              <span className='block text-white text-xl font-normal mt-4'>
                <p>Check out our new font generator <br /> and level up your social bios. <br />Need more?</p>
              </span>
              <Link to="/shopview" className="mt-6 inline-block bg-white text-[#575CEE] px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition duration-300 shadow-md w-40 text-center">
                Shop Now
              </Link>
            </div>

            <div className='flex flex-col justify-center'>
              <img className='h-16 object-contain' src={safe} alt="Safe shopping" />
            </div>

            <div className='flex justify-center'>
              <img className='h-64 object-contain hover:scale-105 transition-transform duration-500' src={anibg} alt="Dog" />
            </div>
          </div>
        </div>
      </section>


      <section className="py-12 bg-white">
        <div className="container mx-auto px-10">
          <div className="grid grid-cols-4 gap-8">
            <div className="flex flex-col items-center text-center p-6 rounded-lg hover:shadow-lg transition-shadow duration-300">
              <div className="bg-[#575CEE] p-4 rounded-full text-white mb-4">
                <FaPaw size={24} />
              </div>
              <h3 className="font-bold text-lg mb-2">Premium Products</h3>
              <p className="text-gray-600 text-sm">High-quality products for your beloved pets</p>
            </div>

            <div className="flex flex-col items-center text-center p-6 rounded-lg hover:shadow-lg transition-shadow duration-300">
              <div className="bg-[#575CEE] p-4 rounded-full text-white mb-4">
                <FaShippingFast size={24} />
              </div>
              <h3 className="font-bold text-lg mb-2">Fast Delivery</h3>
              <p className="text-gray-600 text-sm">Quick shipping to your doorstep</p>
            </div>

            <div className="flex flex-col items-center text-center p-6 rounded-lg hover:shadow-lg transition-shadow duration-300">
              <div className="bg-[#575CEE] p-4 rounded-full text-white mb-4">
                <FaHeadset size={24} />
              </div>
              <h3 className="font-bold text-lg mb-2">24/7 Support</h3>
              <p className="text-gray-600 text-sm">Always here to help with your pet needs</p>
            </div>

            <div className="flex flex-col items-center text-center p-6 rounded-lg hover:shadow-lg transition-shadow duration-300">
              <div className="bg-[#575CEE] p-4 rounded-full text-white mb-4">
                <FaShieldAlt size={24} />
              </div>
              <h3 className="font-bold text-lg mb-2">Secure Payment</h3>
              <p className="text-gray-600 text-sm">Safe and secure payment methods</p>
            </div>
          </div>
        </div>
      </section>


      <section className="py-10 bg-gray-50">
        <PetCategories />
      </section>


      <div className="my-8 flex items-center gap-4 before:h-px before:flex-1 before:bg-gray-300 before:content-[''] after:h-px after:flex-1 after:bg-gray-300 after:content-['']">
        <span className="text-gray-500 font-medium">Featured Products</span>
      </div>


      <div className="bg-white py-6">
        <GenericProductSlider
          products={newArrivals}
          title="New Arrivals"
          className="shadow-sm rounded-xl"
        />
      </div>


      <section className="py-16 bg-[#575CEE] text-white">
        <div className="container mx-auto px-10 text-center">
          <h2 className="text-3xl font-bold mb-4">Special Offer for New Customers</h2>
          <p className="text-xl mb-8">Get 15% off on your first order with code: PAWCARE15</p>
          <Link to="/shopview" className="inline-block bg-white text-[#575CEE] px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition duration-300 shadow-md">
            Shop Now
          </Link>
        </div>
      </section>


      <div className="bg-gray-50 py-10">
        <GuideSlider guides={guideArticles} title="General Guide" />
      </div>


      <section className="py-16 bg-white">
        <div className="container mx-auto px-10 max-w-4xl">
          <div className="bg-gray-100 rounded-2xl p-10 shadow-sm">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-2">Join Our Newsletter</h2>
              <p className="text-gray-600">Stay updated with our latest products and offers</p>
            </div>
            <div className="flex max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-grow px-4 py-3 rounded-l-full border-2 border-[#575CEE] focus:outline-none"
              />
              <button className="bg-[#575CEE] text-white px-6 py-3 rounded-r-full font-semibold hover:bg-[#4a4fd1] transition duration-300">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
