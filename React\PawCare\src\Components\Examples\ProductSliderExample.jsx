import React from 'react';
import GenericProductSlider from '../Slider/GenericProductSlider';
import { newArrivals, petToys, petAccessories } from '../../data/productData';

const ProductSliderExample = () => {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-4xl font-bold text-center mb-10">Product Slider Examples</h1>
      
      {/* Example 1: Basic slider with default settings */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Default Configuration</h2>
        <GenericProductSlider 
          products={newArrivals} 
          title="New Arrivals" 
        />
      </section>
      
      {/* Example 2: Custom number of slides */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Custom Number of Slides (3)</h2>
        <GenericProductSlider 
          products={petToys} 
          title="Pet Toys" 
          slidesToShow={3}
        />
      </section>
      
      {/* Example 3: No autoplay */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">No Autoplay</h2>
        <GenericProductSlider 
          products={petAccessories} 
          title="Pet Accessories" 
          autoplay={false}
        />
      </section>
      
      {/* Example 4: Custom settings */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Custom Settings (Faster, More Slides)</h2>
        <GenericProductSlider 
          products={newArrivals} 
          title="Featured Products" 
          slidesToShow={5}
          autoplaySpeed={1500}
          customSettings={{
            centerMode: true,
            centerPadding: '60px',
          }}
        />
      </section>
      
      {/* Example 5: No title */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">No Title</h2>
        <GenericProductSlider 
          products={petToys.slice(0, 5)} 
          slidesToShow={2}
          className="bg-gray-100 rounded-xl"
        />
      </section>
    </div>
  );
};

export default ProductSliderExample;
